.chat-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: var(--primary-bg);
  position: relative;
}

/* Header */
.chat-area__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--primary-bg);
  min-height: 60px;
}



.chat-area__title h1 {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.chat-area__subtitle {
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: 400;
}

.chat-area__share {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chat-area__share:hover {
  background-color: var(--card-bg);
  color: var(--text-primary);
}

/* Main Content */
.chat-area__content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  padding: 24px;
}

/* Welcome Section */
.chat-area__welcome {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.chat-area__greeting {
  margin-bottom: 48px;
}

.chat-area__greeting h2 {
  font-size: 32px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  line-height: 1.2;
}

/* Prompt Cards */
.chat-area__prompts {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
  width: 100%;
  max-width: 700px;
}

.chat-area__prompt-card {
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 16px;
  text-align: left;
  position: relative;
  overflow: hidden;
}

.chat-area__prompt-card:hover {
  background-color: var(--secondary-bg);
  border-color: var(--accent-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.chat-area__prompt-icon {
  font-size: 24px;
  width: 48px;
  height: 48px;
  background-color: var(--secondary-bg);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.chat-area__prompt-content {
  flex: 1;
  min-width: 0;
}

.chat-area__prompt-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.chat-area__prompt-description {
  font-size: 14px;
  color: var(--text-secondary);
  line-height: 1.4;
}

.chat-area__prompt-add {
  font-size: 20px;
  color: var(--text-secondary);
  font-weight: bold;
  transition: all 0.2s ease;
}

.chat-area__prompt-card:hover .chat-area__prompt-add {
  color: var(--accent-color);
  transform: scale(1.1);
}

/* Messages */
.chat-area__messages {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-width: 800px;
  margin: 0 auto;
  width: 100%;
}

.chat-message {
  display: flex;
  margin-bottom: 16px;
}

.chat-message--user {
  justify-content: flex-end;
}

.chat-message--assistant {
  justify-content: flex-start;
}

.chat-message__content {
  max-width: 70%;
  padding: 12px 16px;
  border-radius: 12px;
  font-size: 14px;
  line-height: 1.5;
}

.chat-message--user .chat-message__content {
  background-color: var(--card-bg);
  color: var(--text-primary);
}

.chat-message--assistant .chat-message__content {
  background-color: var(--secondary-bg);
  color: var(--text-primary);
}

/* Error Display */
.chat-area__error {
  padding: 16px 24px;
  background: rgba(239, 68, 68, 0.1);
  border-top: 1px solid #ef4444;
}

.chat-area__error-content {
  display: flex;
  align-items: center;
  gap: 12px;
  max-width: 800px;
  margin: 0 auto;
}

.chat-area__error-icon {
  font-size: 18px;
  flex-shrink: 0;
}

.chat-area__error-message {
  flex: 1;
  color: #ef4444;
  font-size: 14px;
  font-weight: 500;
}

.chat-area__error-close {
  background: none;
  border: none;
  color: #ef4444;
  cursor: pointer;
  font-size: 18px;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.chat-area__error-close:hover {
  background: rgba(239, 68, 68, 0.2);
}

/* Input Container */
.chat-area__input-container {
  padding: 24px;
  border-top: 1px solid var(--border-color);
  background-color: var(--primary-bg);
}

/* Responsive Design */
@media (max-width: 768px) {
  .chat-area__content {
    padding: 16px;
  }

  .chat-area__greeting h2 {
    font-size: 24px;
  }

  .chat-area__prompts {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .chat-area__prompt-card {
    padding: 16px;
  }

  .chat-area__input-container {
    padding: 16px;
  }

  .chat-message__content {
    max-width: 85%;
  }
}
