import React, { useEffect, useRef } from 'react';
import { useChat } from '../../../contexts/ChatContext';
import './MessageList.css';

const MessageList = () => {
  const { messages, isLoading, hasMoreMessages, loadMessages, currentThread } = useChat();
  const messagesEndRef = useRef(null);
  const messagesContainerRef = useRef(null);

  // Scroll to bottom when new messages arrive
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleScroll = () => {
    const container = messagesContainerRef.current;
    if (container && container.scrollTop === 0 && hasMoreMessages && !isLoading && currentThread) {
      // Load more messages when scrolled to top
      loadMessages(currentThread.id, messages.length, true);
    }
  };

  const formatTime = (timestamp) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const formatMessage = (text) => {
    // Simple markdown-like formatting
    return text
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/`(.*?)`/g, '<code>$1</code>')
      .replace(/\n/g, '<br>');
  };

  if (messages.length === 0 && !isLoading) {
    return (
      <div className="message-list message-list--empty">
        <div className="message-list__empty-state">
          <div className="message-list__empty-icon">💬</div>
          <h3>Start a conversation</h3>
          <p>Send a message to begin chatting with AI</p>
        </div>
      </div>
    );
  }

  return (
    <div 
      className="message-list"
      ref={messagesContainerRef}
      onScroll={handleScroll}
    >
      {isLoading && messages.length === 0 && (
        <div className="message-list__loading">
          <div className="message-list__loading-spinner"></div>
          <p>Loading messages...</p>
        </div>
      )}

      {hasMoreMessages && messages.length > 0 && (
        <div className="message-list__load-more">
          {isLoading ? (
            <div className="message-list__loading-spinner"></div>
          ) : (
            <button 
              onClick={() => loadMessages(currentThread.id, messages.length, true)}
              className="message-list__load-more-btn"
            >
              Load more messages
            </button>
          )}
        </div>
      )}

      <div className="message-list__messages">
        {messages.map((message, index) => (
          <div key={message.id || index} className="message-list__message-group">
            {/* User Message */}
            <div className="message-list__message message-list__message--user">
              <div className="message-list__message-content">
                <div className="message-list__message-text">
                  {message.message}
                </div>
                <div className="message-list__message-time">
                  {formatTime(message.createdAt)}
                </div>
              </div>
              <div className="message-list__message-avatar message-list__message-avatar--user">
                <span>You</span>
              </div>
            </div>

            {/* AI Response */}
            {message.response && (
              <div className="message-list__message message-list__message--ai">
                <div className="message-list__message-avatar message-list__message-avatar--ai">
                  <div className="message-list__ai-icon">🤖</div>
                </div>
                <div className="message-list__message-content">
                  <div className="message-list__message-header">
                    <span className="message-list__message-sender">TheInfini AI</span>
                    {message.llmModel && (
                      <span className="message-list__message-model">{message.llmModel}</span>
                    )}
                  </div>
                  <div 
                    className="message-list__message-text"
                    dangerouslySetInnerHTML={{ __html: formatMessage(message.response) }}
                  />
                  <div className="message-list__message-time">
                    {formatTime(message.createdAt)}
                  </div>
                  <div className="message-list__message-actions">
                    <button 
                      className="message-list__action-btn"
                      onClick={() => navigator.clipboard.writeText(message.response)}
                      title="Copy response"
                    >
                      📋
                    </button>
                    <button 
                      className="message-list__action-btn"
                      title="Regenerate response"
                    >
                      🔄
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>

      <div ref={messagesEndRef} />
    </div>
  );
};

export default MessageList;
