import axios from 'axios';

class CSRFService {
  // Get CSRF token from server
  async getCSRFToken() {
    try {
      const baseURL = process.env.REACT_APP_API_URL || 'http://localhost:5529/api';
      const response = await axios.get(`${baseURL}/auth/csrf-token`);
      
      if (response.data.success) {
        const csrfToken = response.data.data.csrfToken;
        localStorage.setItem('csrfToken', csrfToken);
        return csrfToken;
      }
      
      throw new Error('Failed to get CSRF token');
    } catch (error) {
      console.error('Error getting CSRF token:', error);
      throw error;
    }
  }

  // Get stored CSRF token
  getStoredCSRFToken() {
    return localStorage.getItem('csrfToken');
  }

  // Clear CSRF token
  clearCSRFToken() {
    localStorage.removeItem('csrfToken');
  }

  // Ensure CSRF token is available
  async ensureCSRFToken() {
    let token = this.getStoredCSRFToken();
    
    if (!token) {
      token = await this.getCSRFToken();
    }
    
    return token;
  }
}

const csrfService = new CSRFService();
export default csrfService;
