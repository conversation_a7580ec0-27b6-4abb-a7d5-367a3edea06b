import axios from 'axios';

// Create axios instance with base configuration
const apiClient = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5529/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token and CSRF token
apiClient.interceptors.request.use(
  async (config) => {
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // Add CSRF token for POST, PUT, DELETE requests
    if (['post', 'put', 'delete'].includes(config.method?.toLowerCase())) {
      const csrfToken = localStorage.getItem('csrfToken');
      if (csrfToken) {
        config.headers['X-CSRF-Token'] = csrfToken;
      } else {
        // Try to get CSRF token if not available
        try {
          const csrfResponse = await axios.get(`${config.baseURL}/auth/csrf-token`);
          if (csrfResponse.data.success) {
            const newCsrfToken = csrfResponse.data.data.csrfToken;
            localStorage.setItem('csrfToken', newCsrfToken);
            config.headers['X-CSRF-Token'] = newCsrfToken;
          }
        } catch (csrfError) {
          console.warn('Failed to get CSRF token:', csrfError);
        }
      }
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle common errors
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('authToken');
      localStorage.removeItem('user');
      localStorage.removeItem('csrfToken');
      window.location.href = '/signin';
    }
    return Promise.reject(error);
  }
);

export default apiClient;
