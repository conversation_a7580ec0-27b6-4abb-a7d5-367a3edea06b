# **TheInfini AI Chat Module - Complete API Documentation**

## **Overview**
This document provides comprehensive API documentation for the TheInfini AI chat module, including regular chat threads, project-based chats, and all related functionality. This documentation is designed to help frontend developers integrate with the backend APIs.

---

## **🔐 Authentication**
All API endpoints require JWT authentication unless specified otherwise.

**Headers Required:**
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
X-CSRF-Token: <csrf_token> (for POST/PUT/DELETE operations)
```

**Get CSRF Token:**
```http
GET /api/auth/csrf-token
```

---

## **📋 API Endpoints Overview**

### **1. Thread Management APIs**
| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/api/threads/message` | Send message in thread (regular or project) |
| GET | `/api/threads` | Get user's regular threads |
| GET | `/api/threads/:threadId` | Get thread details |
| GET | `/api/threads/:threadId/messages` | Get thread messages |
| PUT | `/api/threads/:threadId/name` | Update thread name |
| DELETE | `/api/threads/:threadId` | Delete thread |

### **2. Project Management APIs**
| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/api/projects` | Create new project |
| GET | `/api/projects` | Get user's projects |
| GET | `/api/projects/search` | Search projects |
| GET | `/api/projects/:projectId` | Get project details |
| PUT | `/api/projects/:projectId` | Update project |
| DELETE | `/api/projects/:projectId` | Delete project |
| GET | `/api/projects/:projectId/threads` | Get project threads |
| POST | `/api/projects/:projectId/message` | Send message in project thread |
| GET | `/api/projects/:projectId/stats` | Get project statistics |

### **3. Legacy Chat APIs (Still Available)**
| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/api/chat/message` | Send message (legacy, supports guest users) |
| GET | `/api/chat/chats` | Get user chats (legacy) |
| GET | `/api/chat/chats/:chatId/messages` | Get chat messages (legacy) |

---

## **🧵 Thread Management APIs**

### **1. Send Message in Thread**
```http
POST /api/threads/message
```

**Purpose:** Send a message in a regular thread or project thread. Supports RAG with last 5 messages.

**Request Body:**
```json
{
  "message": "Your message here",
  "threadId": "uuid-optional", // If not provided, creates new thread
  "projectId": "uuid-optional", // If provided, creates/uses project thread
  "llmModel": "gpt-3.5-turbo" // Optional, defaults to env setting
}
```

**Response:**
```json
{
  "success": true,
  "message": "Message sent successfully",
  "data": {
    "response": "AI response here",
    "threadId": "thread-uuid",
    "messageId": "message-uuid",
    "isProject": false,
    "projectId": "project-uuid" // Only if project thread
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

**Rate Limit:** 20 messages per minute
**Credits:** Deducts 1 credit per message

---

### **2. Get User Threads**
```http
GET /api/threads?limit=6&offset=0
```

**Purpose:** Get user's regular threads (excludes project threads).

**Query Parameters:**
- `limit` (optional): Number of threads to return (default: 6, max: 50)
- `offset` (optional): Number of threads to skip (default: 0)

**Response:**
```json
{
  "success": true,
  "message": "Threads retrieved successfully",
  "data": {
    "threads": [
      {
        "id": "thread-uuid",
        "userId": "user-uuid",
        "projectId": null,
        "sessionId": "session-id",
        "name": "Thread name...",
        "isGuest": false,
        "createdAt": "2024-01-01T00:00:00.000Z",
        "updatedAt": "2024-01-01T00:00:00.000Z"
      }
    ],
    "limit": 6,
    "offset": 0
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

---

### **3. Get Thread Details**
```http
GET /api/threads/:threadId
```

**Purpose:** Get detailed information about a specific thread.

**Response:**
```json
{
  "success": true,
  "message": "Thread retrieved successfully",
  "data": {
    "id": "thread-uuid",
    "userId": "user-uuid",
    "projectId": null,
    "sessionId": "session-id",
    "name": "Thread name...",
    "isGuest": false,
    "messageCount": 15,
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

---

### **4. Get Thread Messages**
```http
GET /api/threads/:threadId/messages?limit=50&offset=0
```

**Purpose:** Get messages from a specific thread.

**Query Parameters:**
- `limit` (optional): Number of messages to return (default: 50, max: 100)
- `offset` (optional): Number of messages to skip (default: 0)

**Response:**
```json
{
  "success": true,
  "message": "Messages retrieved successfully",
  "data": {
    "messages": [
      {
        "id": "message-uuid",
        "chatId": "thread-uuid",
        "message": "User message",
        "response": "AI response",
        "llmModel": "gpt-3.5-turbo",
        "isUserMessage": true,
        "createdAt": "2024-01-01T00:00:00.000Z"
      }
    ],
    "threadId": "thread-uuid",
    "limit": 50,
    "offset": 0
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

---

### **5. Update Thread Name**
```http
PUT /api/threads/:threadId/name
```

**Purpose:** Update the name of a thread.

**Request Body:**
```json
{
  "name": "New thread name"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Thread name updated successfully",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

**Validation:**
- Name must be 1-255 characters
- Name cannot be empty

---

### **6. Delete Thread**
```http
DELETE /api/threads/:threadId
```

**Purpose:** Delete a thread and all its messages. For project threads, also removes Pinecone data.

**Response:**
```json
{
  "success": true,
  "message": "Thread deleted successfully",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

---

## **📁 Project Management APIs**

### **1. Create Project**
```http
POST /api/projects
```

**Purpose:** Create a new project with name, description, and rules.

**Request Body:**
```json
{
  "name": "Project Name",
  "description": "Detailed project description that will be included in AI context",
  "rules": "Project-specific rules and guidelines for AI responses"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Project created successfully",
  "data": {
    "id": "project-uuid",
    "userId": "user-uuid",
    "name": "Project Name",
    "description": "Project description",
    "rules": "Project rules",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

**Rate Limit:** 10 project creations per minute

**Validation:**
- Name: 1-255 characters, required
- Description: 1-2000 characters, required
- Rules: 1-2000 characters, required

---

### **2. Get User Projects**
```http
GET /api/projects?limit=6&offset=0
```

**Purpose:** Get user's projects with pagination.

**Query Parameters:**
- `limit` (optional): Number of projects to return (default: 6, max: 50)
- `offset` (optional): Number of projects to skip (default: 0)

**Response:**
```json
{
  "success": true,
  "message": "Projects retrieved successfully",
  "data": {
    "projects": [
      {
        "id": "project-uuid",
        "userId": "user-uuid",
        "name": "Project Name",
        "description": "Project description",
        "rules": "Project rules",
        "createdAt": "2024-01-01T00:00:00.000Z",
        "updatedAt": "2024-01-01T00:00:00.000Z"
      }
    ],
    "limit": 6,
    "offset": 0
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

---

### **3. Search Projects**
```http
GET /api/projects/search?q=search_term&limit=6&offset=0
```

**Purpose:** Search projects by name.

**Query Parameters:**
- `q`: Search term (required, 1-100 characters)
- `limit` (optional): Number of results to return (default: 6, max: 50)
- `offset` (optional): Number of results to skip (default: 0)

**Response:**
```json
{
  "success": true,
  "message": "Projects search completed",
  "data": {
    "projects": [
      {
        "id": "project-uuid",
        "userId": "user-uuid",
        "name": "Matching Project Name",
        "description": "Project description",
        "rules": "Project rules",
        "createdAt": "2024-01-01T00:00:00.000Z",
        "updatedAt": "2024-01-01T00:00:00.000Z"
      }
    ],
    "searchTerm": "search_term",
    "limit": 6,
    "offset": 0
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

---

## **🚀 Frontend Development Prompt**

Use this comprehensive prompt to guide LLM in building the frontend:

---

# **Frontend Development TheInfini AI Chat Module**

## **Requirements:**

### **2. UI/UX Requirements:**
- Dark theme with #fcd469 color for call-to-action buttons
- Clean, modern design following industry standards
- Responsive design (mobile-first approach)
- Smooth animations and transitions

### **3. Core Components to Build:**

#### **Chat Interface:**
- Unified chat component supporting both regular and project threads
- Message input with attach, talk, and model selection options
- Real-time message display with user/AI message differentiation
- Thread switching sidebar
- Message history with pagination

#### **Project Management:**
- Project creation modal with name, description, and rules fields
- Project listing with search functionality
- Project editing capabilities
- Project statistics display
- Project thread management

#### **Thread Management:**
- Thread listing with pagination (offset=6)
- Thread renaming functionality
- Thread deletion with confirmation
- Auto-generated thread names from first 10 characters

#### **Navigation:**
- Left sidebar with:
  - New chat button
  - Projects section
  - Regular threads section
  - Search functionality
  - Settings at bottom
  - User profile at bottom

### **4. Key Features:**
- JWT authentication with automatic token refresh
- CSRF token handling
- Credit system display and warnings
- Rate limiting handling
- Error handling and user feedback
- Loading states and skeletons
- Infinite scroll for message history

### **5. API Integration:**
- Use the documented APIs above
- Implement proper error handling
- Handle rate limiting gracefully
- Show credit warnings when low
- Implement optimistic updates where appropriate

### **6. State Management:**
- Manage authentication state
- Handle chat threads and projects
- Cache messages and threads
- Implement proper loading states

### **7. User Experience:**
- Smooth transitions between threads/projects
- Auto-save draft messages
- Keyboard shortcuts for common actions
- Mobile-responsive design
- Accessibility compliance

Build a production-ready, scalable frontend that provides an excellent user experience for the chat module functionality described in the API documentation above.

---

This documentation and prompt provide everything needed to build a comprehensive frontend for the chat module. The APIs are fully implemented and ready for integration.

---

### **4. Get Project Details**
```http
GET /api/projects/:projectId
```

**Purpose:** Get detailed information about a specific project including statistics.

**Response:**
```json
{
  "success": true,
  "message": "Project retrieved successfully",
  "data": {
    "id": "project-uuid",
    "userId": "user-uuid",
    "name": "Project Name",
    "description": "Project description",
    "rules": "Project rules",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z",
    "stats": {
      "threadCount": 5,
      "messageCount": 42,
      "lastActivity": "2024-01-01T00:00:00.000Z"
    }
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

---

### **5. Update Project**
```http
PUT /api/projects/:projectId
```

**Purpose:** Update project details.

**Request Body:**
```json
{
  "name": "Updated Project Name", // Optional
  "description": "Updated description", // Optional
  "rules": "Updated rules" // Optional
}
```

**Response:**
```json
{
  "success": true,
  "message": "Project updated successfully",
  "data": {
    "id": "project-uuid",
    "userId": "user-uuid",
    "name": "Updated Project Name",
    "description": "Updated description",
    "rules": "Updated rules",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

---

### **6. Delete Project**
```http
DELETE /api/projects/:projectId
```

**Purpose:** Delete a project and all associated threads, messages, and Pinecone data.

**Response:**
```json
{
  "success": true,
  "message": "Project deleted successfully",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

---

### **7. Get Project Threads**
```http
GET /api/projects/:projectId/threads?limit=6&offset=0
```

**Purpose:** Get threads within a specific project.

**Query Parameters:**
- `limit` (optional): Number of threads to return (default: 6, max: 50)
- `offset` (optional): Number of threads to skip (default: 0)

**Response:**
```json
{
  "success": true,
  "message": "Project threads retrieved successfully",
  "data": {
    "threads": [
      {
        "id": "thread-uuid",
        "userId": "user-uuid",
        "projectId": "project-uuid",
        "sessionId": "session-id",
        "name": "Thread name...",
        "isGuest": false,
        "createdAt": "2024-01-01T00:00:00.000Z",
        "updatedAt": "2024-01-01T00:00:00.000Z"
      }
    ],
    "projectId": "project-uuid",
    "limit": 6,
    "offset": 0
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

---

### **8. Send Message in Project Thread**
```http
POST /api/projects/:projectId/message
```

**Purpose:** Send a message in a project thread. Includes project context and Pinecone RAG.

**Request Body:**
```json
{
  "message": "Your message here",
  "threadId": "uuid-optional", // If not provided, creates new project thread
  "llmModel": "gpt-3.5-turbo" // Optional
}
```

**Response:**
```json
{
  "success": true,
  "message": "Message sent successfully",
  "data": {
    "response": "AI response with project context",
    "threadId": "thread-uuid",
    "messageId": "message-uuid",
    "isProject": true,
    "projectId": "project-uuid"
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

**Rate Limit:** 20 messages per minute
**Credits:** Deducts 1 credit per message

---

### **9. Get Project Statistics**
```http
GET /api/projects/:projectId/stats
```

**Purpose:** Get detailed statistics for a project.

**Response:**
```json
{
  "success": true,
  "message": "Project statistics retrieved successfully",
  "data": {
    "threadCount": 5,
    "messageCount": 42,
    "lastActivity": "2024-01-01T00:00:00.000Z"
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

---

## **🔄 Legacy Chat APIs (Backward Compatibility)**

### **Send Legacy Chat Message**
```http
POST /api/chat/message
```

**Purpose:** Legacy endpoint for sending chat messages. Supports both authenticated and guest users.

**Request Body:**
```json
{
  "message": "Your message here",
  "sessionId": "session-id-optional", // For guest users
  "llmModel": "gpt-3.5-turbo" // Optional
}
```

**Response:**
```json
{
  "success": true,
  "message": "Message sent successfully",
  "data": {
    "response": "AI response",
    "sessionId": "session-id",
    "messageId": "message-uuid",
    "isGuest": true
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

---

## **⚠️ Error Responses**

All endpoints return consistent error responses:

```json
{
  "success": false,
  "message": "Error description",
  "error": "Detailed error information",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

**Common HTTP Status Codes:**
- `200` - Success
- `400` - Bad Request (validation errors)
- `401` - Unauthorized (invalid/missing token)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found
- `429` - Too Many Requests (rate limit exceeded)
- `500` - Internal Server Error

---

## **💳 Credit System**

**Credit Costs:**
- Regular thread message: 1 credit
- Project thread message: 1 credit
- Legacy chat message: 1 credit

**Credit Checks:**
- All message endpoints check for sufficient credits before processing
- Returns error if insufficient credits available

---

## **🔧 Rate Limits**

**Per User Limits:**
- Chat messages: 20 per minute
- Project creation: 10 per minute
- Other endpoints: Standard rate limiting applies

---

## **📱 Frontend Integration Guide**

### **Recommended Implementation Flow:**

1. **Authentication Setup**
   - Implement JWT token management
   - Handle CSRF token retrieval and inclusion
   - Set up automatic token refresh

2. **Chat Interface**
   - Create unified chat component that works with both regular and project threads
   - Implement real-time message display
   - Add thread switching functionality

3. **Project Management**
   - Create project creation/editing forms
   - Implement project listing with search
   - Add project statistics dashboard

4. **Thread Management**
   - Implement thread listing with pagination
   - Add thread renaming functionality
   - Create thread deletion with confirmation

5. **Error Handling**
   - Implement consistent error display
   - Handle rate limiting gracefully
   - Show credit insufficient warnings

### **State Management Recommendations:**

```javascript
// Example state structure
const chatState = {
  currentThread: null,
  currentProject: null,
  threads: [],
  projects: [],
  messages: [],
  isLoading: false,
  error: null
};
```

### **API Client Example:**

```javascript
class ChatAPI {
  constructor(baseURL, getToken, getCSRFToken) {
    this.baseURL = baseURL;
    this.getToken = getToken;
    this.getCSRFToken = getCSRFToken;
  }

  async sendMessage(data) {
    return this.post('/threads/message', data);
  }

  async createProject(data) {
    return this.post('/projects', data);
  }

  async getThreads(limit = 6, offset = 0) {
    return this.get(`/threads?limit=${limit}&offset=${offset}`);
  }

  // ... other methods
}
```
